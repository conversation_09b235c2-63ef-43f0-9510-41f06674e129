import React from 'react';
import { render } from '@testing-library/react';
import { Canvas } from '@react-three/fiber';
import { Avatar } from '../Avatar';

// Mock the useGLTF hook to simulate loading states
jest.mock('@react-three/drei', () => ({
  useGLTF: jest.fn(),
  useAnimations: jest.fn(),
}));

// Mock the useChat hook
jest.mock('../../hooks/useChat', () => ({
  useChat: () => ({
    message: null,
    onMessagePlayed: jest.fn(),
    chat: jest.fn(),
  }),
}));

// Mock leva controls
jest.mock('leva', () => ({
  useControls: jest.fn(() => [null, jest.fn()]),
  button: jest.fn(() => jest.fn()),
}));

describe('Avatar Component', () => {
  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
  });

  test('should handle undefined animations gracefully', () => {
    const { useGLTF, useAnimations } = require('@react-three/drei');
    
    // Mock useGLTF to return undefined animations
    useGLTF.mockReturnValue({
      nodes: {},
      materials: {},
      scene: { traverse: jest.fn() },
      animations: undefined,
    });
    
    // Mock useAnimations to return empty actions
    useAnimations.mockReturnValue({
      actions: {},
      mixer: null,
    });

    // This should not throw an error
    expect(() => {
      render(
        <Canvas>
          <Avatar />
        </Canvas>
      );
    }).not.toThrow();
  });

  test('should handle empty animations array gracefully', () => {
    const { useGLTF, useAnimations } = require('@react-three/drei');
    
    // Mock useGLTF to return empty animations array
    useGLTF.mockReturnValue({
      nodes: {},
      materials: {},
      scene: { traverse: jest.fn() },
      animations: [],
    });
    
    // Mock useAnimations to return empty actions
    useAnimations.mockReturnValue({
      actions: {},
      mixer: null,
    });

    // This should not throw an error
    expect(() => {
      render(
        <Canvas>
          <Avatar />
        </Canvas>
      );
    }).not.toThrow();
  });

  test('should handle missing nodes gracefully', () => {
    const { useGLTF, useAnimations } = require('@react-three/drei');
    
    // Mock useGLTF to return undefined nodes
    useGLTF.mockReturnValue({
      nodes: undefined,
      materials: undefined,
      scene: { traverse: jest.fn() },
      animations: [{ name: 'Idle' }],
    });
    
    // Mock useAnimations
    useAnimations.mockReturnValue({
      actions: { Idle: { reset: jest.fn().mockReturnThis(), fadeIn: jest.fn().mockReturnThis(), play: jest.fn(), fadeOut: jest.fn() } },
      mixer: { stats: { actions: { inUse: 0 } } },
    });

    // This should not throw an error and should return null
    expect(() => {
      render(
        <Canvas>
          <Avatar />
        </Canvas>
      );
    }).not.toThrow();
  });
});
