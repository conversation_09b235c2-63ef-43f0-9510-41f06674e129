<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facial Expression Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        .container {
            display: flex;
            height: 100vh;
        }
        .controls {
            width: 300px;
            background: white;
            padding: 20px;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            overflow-y: auto;
        }
        .canvas-container {
            flex: 1;
            position: relative;
        }
        .expression-btn {
            display: block;
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            background: #f8f9fa;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.2s;
        }
        .expression-btn:hover {
            background: #e9ecef;
        }
        .expression-btn.active {
            background: #007bff;
            color: white;
        }
        .debug-info {
            margin-top: 20px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 12px;
        }
        .test-btn {
            width: 100%;
            padding: 12px;
            margin: 10px 0;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-btn:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="controls">
            <h2>Facial Expression Test</h2>
            <p>Click on expressions to test them:</p>
            
            <button class="expression-btn active" data-expression="">Default</button>
            <button class="expression-btn" data-expression="smile">Smile</button>
            <button class="expression-btn" data-expression="sad">Sad</button>
            <button class="expression-btn" data-expression="surprised">Surprised</button>
            <button class="expression-btn" data-expression="angry">Angry</button>
            <button class="expression-btn" data-expression="crazy">Crazy</button>
            <button class="expression-btn" data-expression="funnyFace">Funny Face</button>
            
            <button class="test-btn" id="testAll">Test All Expressions</button>
            <button class="test-btn" id="logMorphTargets">Log Morph Targets</button>
            
            <div class="debug-info">
                <strong>Current Expression:</strong> <span id="currentExpression">default</span><br>
                <strong>Status:</strong> <span id="status">Ready</span><br>
                <strong>Instructions:</strong><br>
                1. Open browser console (F12)<br>
                2. Click expressions to test<br>
                3. Watch for debug logs<br>
                4. Check if avatar face changes
            </div>
        </div>
        
        <div class="canvas-container">
            <iframe 
                src="http://localhost:5173/" 
                width="100%" 
                height="100%" 
                frameborder="0"
                id="avatarFrame">
            </iframe>
        </div>
    </div>

    <script>
        let currentExpression = '';
        
        // Expression buttons
        document.querySelectorAll('.expression-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const expression = btn.dataset.expression;
                setExpression(expression);
                
                // Update UI
                document.querySelectorAll('.expression-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });
        
        // Test all button
        document.getElementById('testAll').addEventListener('click', () => {
            const expressions = ['', 'smile', 'sad', 'surprised', 'angry', 'crazy', 'funnyFace'];
            let index = 0;
            
            document.getElementById('status').textContent = 'Testing all expressions...';
            
            const testNext = () => {
                if (index < expressions.length) {
                    setExpression(expressions[index]);
                    document.getElementById('status').textContent = `Testing: ${expressions[index] || 'default'} (${index + 1}/${expressions.length})`;
                    index++;
                    setTimeout(testNext, 3000);
                } else {
                    document.getElementById('status').textContent = 'Test complete';
                }
            };
            
            testNext();
        });
        
        // Log morph targets button
        document.getElementById('logMorphTargets').addEventListener('click', () => {
            console.log('Requesting morph target log...');
            // This would trigger the logAllMorphTargets button in Leva
            document.getElementById('status').textContent = 'Check console for morph targets';
        });
        
        function setExpression(expression) {
            currentExpression = expression;
            document.getElementById('currentExpression').textContent = expression || 'default';
            
            // Log to console for debugging
            console.log(`Setting facial expression to: "${expression}"`);
            
            // In a real implementation, this would communicate with the React app
            // For now, we'll just log and rely on manual testing with Leva controls
        }
        
        // Initialize
        setExpression('');
    </script>
</body>
</html>
