import React, { useState } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import { Avatar } from './Avatar';

const facialExpressions = [
  'default',
  'smile', 
  'sad',
  'surprised',
  'angry',
  'crazy',
  'funnyFace'
];

export const FacialExpressionTest = () => {
  const [currentExpression, setCurrentExpression] = useState('default');

  return (
    <div style={{ width: '100vw', height: '100vh', position: 'relative' }}>
      {/* Controls Panel */}
      <div style={{
        position: 'absolute',
        top: '20px',
        left: '20px',
        zIndex: 1000,
        background: 'rgba(255, 255, 255, 0.9)',
        padding: '20px',
        borderRadius: '10px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
      }}>
        <h3>Facial Expression Test</h3>
        <p>Current: <strong>{currentExpression}</strong></p>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
          {facialExpressions.map(expression => (
            <button
              key={expression}
              onClick={() => setCurrentExpression(expression)}
              style={{
                padding: '8px 16px',
                backgroundColor: currentExpression === expression ? '#007bff' : '#f8f9fa',
                color: currentExpression === expression ? 'white' : 'black',
                border: '1px solid #dee2e6',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              {expression}
            </button>
          ))}
        </div>
        <div style={{ marginTop: '20px' }}>
          <button
            onClick={() => {
              console.log('Testing all expressions...');
              facialExpressions.forEach((expr, index) => {
                setTimeout(() => {
                  console.log(`Setting expression to: ${expr}`);
                  setCurrentExpression(expr);
                }, index * 2000);
              });
            }}
            style={{
              padding: '10px 20px',
              backgroundColor: '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Test All Expressions
          </button>
        </div>
      </div>

      {/* 3D Scene */}
      <Canvas camera={{ position: [0, 0, 5], fov: 30 }}>
        <ambientLight intensity={0.5} />
        <directionalLight position={[10, 10, 5]} intensity={1} />
        <OrbitControls enablePan={true} enableZoom={true} enableRotate={true} />
        
        {/* Force the Avatar to use our test expression */}
        <TestAvatar expression={currentExpression} />
      </Canvas>
    </div>
  );
};

// Wrapper component to force facial expression
const TestAvatar = ({ expression }) => {
  // This component will force the Avatar to use our test expression
  React.useEffect(() => {
    console.log(`TestAvatar: Setting expression to ${expression}`);
  }, [expression]);

  return <Avatar testExpression={expression} />;
};
